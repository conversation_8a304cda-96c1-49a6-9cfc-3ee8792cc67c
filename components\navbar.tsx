"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import Link from "next/link"
import { FileText, ChevronRight, User, Code, FolderOpen, Briefcase, Mail, Menu, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { useActiveSection } from "@/hooks/use-active-section"
import { useIsMobile } from "@/hooks/use-mobile"

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [showNav, setShowNav] = useState(false)
  const [clickedItem, setClickedItem] = useState<string | null>(null)

  // Use optimized hook for active section detection
  const activeSection = useActiveSection()
  const isMobile = useIsMobile()

  // Optimized scroll handler with throttling
  useEffect(() => {
    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setShowNav(true)
          setScrolled(window.scrollY > 10)
          ticking = false
        })
        ticking = true
      }
    }

    setShowNav(true)
    handleScroll()

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => {
      window.removeEventListener("scroll", handleScroll)
      // Cleanup: remove mobile nav class and restore scrolling when component unmounts
      document.body.classList.remove('mobile-nav-open')
      document.body.style.overflow = ''
    }
  }, [])

  // Optimized toggle with useCallback to prevent re-renders
  const toggleMenu = useCallback(() => {
    setIsOpen(prev => {
      const newState = !prev
      // Use optimized backdrop approach for mobile
      if (newState) {
        document.body.classList.add('mobile-nav-open')
        // Prevent scrolling when mobile menu is open
        if (isMobile) {
          document.body.style.overflow = 'hidden'
        }
      } else {
        document.body.classList.remove('mobile-nav-open')
        // Restore scrolling
        document.body.style.overflow = ''
      }
      return newState
    })
  }, [isMobile])

  // Optimized smooth scroll with reduced complexity
  const smoothScrollTo = useCallback((targetId: string) => {
    const element = document.getElementById(targetId)
    if (element) {
      // Close mobile menu if open
      setIsOpen(false)
      // Remove mobile nav class and restore scrolling
      document.body.classList.remove('mobile-nav-open')
      document.body.style.overflow = ''

      // Add click animation feedback
      setClickedItem(targetId)
      setTimeout(() => setClickedItem(null), isMobile ? 150 : 200)

      // Use native smooth scroll for better performance
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })
    }
  }, [isMobile])

  // Memoize navLinks to prevent re-creation on every render
  const navLinks = useMemo(() => [
    { name: "About", href: "#about", icon: User },
    { name: "Skills", href: "#skills", icon: Code },
    { name: "Projects", href: "#projects", icon: FolderOpen },
    { name: "Experience", href: "#experience", icon: Briefcase },
    { name: "Contact", href: "#contact", icon: Mail },
  ], [])

  // Mobile-optimized animation variants
  const mobileMenuVariants = useMemo(() => ({
    initial: { opacity: 0, scale: 0.95, y: -10 },
    animate: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95, y: -10 }
  }), [])

  const mobileItemVariants = useMemo(() => ({
    initial: { opacity: 0, x: 15 },
    animate: { opacity: 1, x: 0 }
  }), [])

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-500 flex justify-center",
        showNav ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-full",
        scrolled ? "pt-4" : "pt-6"
      )}
    >
      {/* Desktop Navigation */}
      <nav className={cn(
        "hidden md:flex items-center px-6 py-3 rounded-full transition-all duration-500",
        "bg-black/20 backdrop-blur-xl border border-white/10",
        "shadow-lg shadow-black/20",
        scrolled
          ? "bg-black/30 border-white/20"
          : "bg-black/10 border-white/5"
      )}>
        <div className="flex items-center space-x-1">
          {navLinks.map((link) => {
            const isActive = activeSection === link.href.substring(1)
            const isClicked = clickedItem === link.href.substring(1)

            return (
              <button
                key={link.name}
                onClick={() => smoothScrollTo(link.href.substring(1))}
                className={cn(
                  "px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 relative group",
                  isActive
                    ? "text-white bg-white/10 shadow-lg"
                    : "text-white/70 hover:text-white hover:bg-white/5",
                  isClicked && "scale-95 bg-primary/20"
                )}
                style={{ willChange: 'transform' }}
              >
                {isActive && (
                  <motion.span
                    layoutId="navbar-active-pill"
                    className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                    transition={{ type: "tween", duration: 0.2, ease: "easeOut" }}
                  />
                )}
                {link.name}
              </button>
            )
          })}

          <div className="w-px h-6 bg-white/20 mx-2" />

          <Button
            size="sm"
            className="bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-full px-4 py-2 shadow-lg"
            onClick={() => window.open("https://drive.google.com/file/d/1cB98YnieMnp488ptjxFEjATda_TXgj8t/view?usp=sharing", "_blank")}
          >
            <FileText size={14} className="mr-1" />
            Resume
            <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
          </Button>
        </div>
      </nav>

      {/* Mobile Hamburger Menu Button */}
      <button
        className="md:hidden fixed top-4 right-4 z-[10000] text-white bg-black/30 backdrop-blur-xl p-3 rounded-full border border-white/10 hover:border-primary/30 transition-all duration-300 shadow-lg"
        onClick={toggleMenu}
        aria-label="Toggle menu"
      >
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Mobile Navigation Menu */}
      <AnimatePresence mode="wait">
        {isOpen && (
          <motion.div
              variants={mobileMenuVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{
                duration: 0.2,
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "tween"
              }}
              className="md:hidden fixed top-20 right-4 z-[9999] bg-black/60 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
              style={{ willChange: 'transform, opacity' }}
              data-mobile-nav
            >
              <div className="p-6 flex flex-col space-y-4 min-w-[200px]">
                {navLinks.map((link, index) => {
                  const IconComponent = link.icon
                  const isActive = activeSection === link.href.substring(1)
                  const isClicked = clickedItem === link.href.substring(1)

                  return (
                    <motion.button
                      key={link.name}
                      variants={mobileItemVariants}
                      initial="initial"
                      animate="animate"
                      transition={{
                        duration: 0.15,
                        delay: index * 0.04,
                        ease: "easeOut"
                      }}
                      onClick={() => smoothScrollTo(link.href.substring(1))}
                      className={cn(
                        "flex items-center space-x-3 py-3 px-4 rounded-full text-left transition-colors duration-150 w-full",
                        isActive
                          ? "text-white bg-white/10 shadow-lg"
                          : "text-white/80 hover:text-white hover:bg-white/5",
                        isClicked && "scale-95 bg-primary/20"
                      )}
                      style={{ willChange: 'transform' }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <IconComponent size={20} />
                      <span className="font-medium">{link.name}</span>
                    </motion.button>
                  )
                })}

                {/* Separator */}
                <div className="w-full h-px bg-white/20 my-2" />

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.1, delay: 0.1 }}
                >
                  <Button
                    className="w-full bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-colors duration-150 border-none rounded-full shadow-lg"
                    size="sm"
                    onClick={() => {
                      window.open("https://drive.google.com/file/d/1cB98YnieMnp488ptjxFEjATda_TXgj8t/view?usp=sharing", "_blank")
                      setIsOpen(false)
                      document.body.classList.remove('mobile-nav-open')
                    }}
                  >
                    <FileText size={14} className="mr-2" />
                    Resume
                    <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-150" />
                  </Button>
                </motion.div>
              </div>
            </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
