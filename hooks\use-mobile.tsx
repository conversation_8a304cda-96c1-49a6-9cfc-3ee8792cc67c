import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

// Enhanced mobile detection for real devices vs browser simulation
export function useIsRealMobile() {
  const [isRealMobile, setIsRealMobile] = React.useState(false)
  const isMobile = useIsMobile()

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    // Detect real mobile devices vs browser simulation
    const userAgent = navigator.userAgent.toLowerCase()
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const isActualMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

    // Additional checks for mobile performance characteristics
    const hasLimitedMemory = (navigator as any).deviceMemory && (navigator as any).deviceMemory <= 4
    const hasSlowConnection = (navigator as any).connection &&
      ((navigator as any).connection.effectiveType === 'slow-2g' ||
       (navigator as any).connection.effectiveType === '2g' ||
       (navigator as any).connection.effectiveType === '3g')

    setIsRealMobile(isMobile && isTouchDevice && (isActualMobile || hasLimitedMemory || hasSlowConnection))
  }, [isMobile])

  return isRealMobile
}
