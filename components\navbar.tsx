"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { FileText, ChevronRight, User, Code, FolderOpen, Briefcase, Mail } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { useActiveSection } from "@/hooks/use-active-section"

export function Navbar() {
  const [scrolled, setScrolled] = useState(false)
  const [showNav, setShowNav] = useState(false)
  const [clickedItem, setClickedItem] = useState<string | null>(null)
  const [mobileMoreOpen, setMobileMoreOpen] = useState(false)

  const activeSection = useActiveSection()

  useEffect(() => {
    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setShowNav(true)
          setScrolled(window.scrollY > 10)
          ticking = false
        })
        ticking = true
      }
    }

    setShowNav(true)
    handleScroll()

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  const smoothScrollTo = useCallback((targetId: string) => {
    const element = document.getElementById(targetId)
    if (element) {
      // Close mobile more menu if open
      setMobileMoreOpen(false)

      setClickedItem(targetId)
      setTimeout(() => setClickedItem(null), 200)

      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })
    }
  }, [])

  const navLinks = useMemo(() => [
    { name: "About", href: "#about", icon: User },
    { name: "Skills", href: "#skills", icon: Code },
    { name: "Projects", href: "#projects", icon: FolderOpen },
    { name: "Experience", href: "#experience", icon: Briefcase },
    { name: "Contact", href: "#contact", icon: Mail },
  ], [])

  // Mobile navigation configuration - prioritize most important items
  const mobileNavConfig = useMemo(() => {
    const primaryItems = navLinks.slice(0, 3) // About, Skills, Projects
    const moreItems = navLinks.slice(3) // Experience, Contact
    return { primaryItems, moreItems }
  }, [navLinks])

  const toggleMobileMore = useCallback(() => {
    setMobileMoreOpen(prev => !prev)
  }, [])

  // Close mobile more menu when clicking outside
  useEffect(() => {
    if (!mobileMoreOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as Element).closest('[data-mobile-more]')) {
        setMobileMoreOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [mobileMoreOpen])

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-500 flex justify-center",
        showNav ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-full",
        scrolled ? "pt-4" : "pt-6"
      )}
    >
      {/* Desktop Navigation */}
      <nav className={cn(
        "hidden md:flex items-center px-6 py-3 rounded-full transition-all duration-500",
        "bg-black/20 backdrop-blur-xl border border-white/10",
        "shadow-lg shadow-black/20",
        scrolled
          ? "bg-black/30 border-white/20"
          : "bg-black/10 border-white/5"
      )}>
        <div className="flex items-center space-x-1">
          {navLinks.map((link) => {
            const isActive = activeSection === link.href.substring(1)
            const isClicked = clickedItem === link.href.substring(1)

            return (
              <button
                key={link.name}
                onClick={() => smoothScrollTo(link.href.substring(1))}
                className={cn(
                  "px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 relative group",
                  isActive
                    ? "text-white bg-white/10 shadow-lg"
                    : "text-white/70 hover:text-white hover:bg-white/5",
                  isClicked && "scale-95 bg-primary/20"
                )}
                style={{ willChange: 'transform' }}
              >
                {isActive && (
                  <motion.span
                    layoutId="navbar-active-pill"
                    className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                    transition={{ type: "tween", duration: 0.2, ease: "easeOut" }}
                  />
                )}
                {link.name}
              </button>
            )
          })}

          <div className="w-px h-6 bg-white/20 mx-2" />

          <Button
            size="sm"
            className="bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-full px-4 py-2 shadow-lg"
            onClick={() => window.open("https://drive.google.com/file/d/1hxZtbXkdk1GbSVsn7Gr3yj6uxJaL194l/view?usp=drive_link", "_blank")}
          >
            <FileText size={14} className="mr-1" />
            Resume
            <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
          </Button>
        </div>
      </nav>

      {/* Mobile Horizontal Navigation */}
      <motion.nav
        className={cn(
          "md:hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50",
          "bg-black/20 backdrop-blur-xl border border-white/10 rounded-full",
          "shadow-lg shadow-black/20 px-4 py-2",
          scrolled
            ? "bg-black/30 border-white/20"
            : "bg-black/10 border-white/5"
        )}
        initial={{ opacity: 0, y: 100 }}
        animate={{
          opacity: showNav ? 1 : 0,
          y: showNav ? 0 : 100
        }}
        transition={{
          duration: 0.5,
          ease: [0.4, 0.0, 0.2, 1]
        }}
      >
        <div className="flex items-center space-x-1">
          {/* Primary Navigation Items */}
          {mobileNavConfig.primaryItems.map((link) => {
            const isActive = activeSection === link.href.substring(1)
            const isClicked = clickedItem === link.href.substring(1)

            return (
              <button
                key={link.name}
                onClick={() => smoothScrollTo(link.href.substring(1))}
                className={cn(
                  "px-3 py-2 rounded-full text-xs font-medium transition-all duration-300 relative group",
                  isActive
                    ? "text-white bg-white/10 shadow-lg"
                    : "text-white/70 hover:text-white hover:bg-white/5",
                  isClicked && "scale-95 bg-primary/20"
                )}
                style={{ willChange: 'transform' }}
              >
                {isActive && (
                  <motion.span
                    layoutId="mobile-navbar-active-pill"
                    className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                    transition={{ type: "tween", duration: 0.2, ease: "easeOut" }}
                  />
                )}
                {link.name}
              </button>
            )
          })}

          {/* More Button */}
          <div className="relative" data-mobile-more>
            <button
              onClick={toggleMobileMore}
              className={cn(
                "px-3 py-2 rounded-full text-xs font-medium transition-all duration-300 relative group",
                mobileMoreOpen
                  ? "text-white bg-white/10 shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/5"
              )}
              style={{ willChange: 'transform' }}
            >
              {mobileMoreOpen && (
                <motion.span
                  layoutId="mobile-more-active-pill"
                  className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                  transition={{ type: "tween", duration: 0.2, ease: "easeOut" }}
                />
              )}
              More
            </button>

            {/* More Dropdown */}
            <AnimatePresence>
              {mobileMoreOpen && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: 10 }}
                  transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
                  className="absolute bottom-full mb-2 right-0 bg-black/60 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl overflow-hidden min-w-[140px]"
                >
                  <div className="p-3 space-y-2">
                    {mobileNavConfig.moreItems.map((link) => {
                      const IconComponent = link.icon
                      const isActive = activeSection === link.href.substring(1)
                      const isClicked = clickedItem === link.href.substring(1)

                      return (
                        <button
                          key={link.name}
                          onClick={() => {
                            smoothScrollTo(link.href.substring(1))
                            setMobileMoreOpen(false)
                          }}
                          className={cn(
                            "flex items-center space-x-2 w-full py-2 px-3 rounded-lg text-xs font-medium transition-all duration-200",
                            isActive
                              ? "text-white bg-white/10 shadow-lg"
                              : "text-white/80 hover:text-white hover:bg-white/5",
                            isClicked && "scale-95 bg-primary/20"
                          )}
                          style={{ willChange: 'transform' }}
                        >
                          <IconComponent size={16} />
                          <span>{link.name}</span>
                        </button>
                      )
                    })}

                    <div className="w-full h-px bg-white/20 my-2" />

                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-lg px-3 py-2 shadow-lg text-xs"
                      onClick={() => {
                        window.open("https://drive.google.com/file/d/1hxZtbXkdk1GbSVsn7Gr3yj6uxJaL194l/view?usp=drive_link", "_blank")
                        setMobileMoreOpen(false)
                      }}
                    >
                      <FileText size={12} className="mr-1" />
                      Resume
                      <ChevronRight size={12} className="ml-1 opacity-0 -translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.nav>
    </header>
  )
}
