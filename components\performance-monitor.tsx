"use client"

import { useEffect, useState } from "react"

interface PerformanceMetrics {
  fps: number
  frameTime: number
  isOptimal: boolean
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    frameTime: 0,
    isOptimal: false
  })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const measurePerformance = (currentTime: number) => {
      frameCount++
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        const frameTime = (currentTime - lastTime) / frameCount
        
        setMetrics({
          fps,
          frameTime: Math.round(frameTime * 100) / 100,
          isOptimal: fps >= 55 // Consider 55+ FPS as optimal for mobile
        })
        
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(measurePerformance)
    }

    // Only show in development
    if (process.env.NODE_ENV === 'development') {
      setIsVisible(true)
      animationId = requestAnimationFrame(measurePerformance)
    }

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [])

  // Show performance monitor with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  if (!isVisible) return null

  return (
    <div className="fixed bottom-4 left-4 z-[10000] bg-black/80 backdrop-blur-sm border border-white/20 rounded-lg p-3 text-xs text-white font-mono">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div 
            className={`w-2 h-2 rounded-full ${
              metrics.isOptimal ? 'bg-green-400' : 'bg-red-400'
            }`}
          />
          <span>FPS: {metrics.fps}</span>
        </div>
        <div>Frame: {metrics.frameTime}ms</div>
        <div className="text-xs opacity-70">
          Ctrl+Shift+P to toggle
        </div>
      </div>
    </div>
  )
}
